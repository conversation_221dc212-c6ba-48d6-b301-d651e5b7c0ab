import { type NextRequest, NextResponse } from "next/server"
import { convertAnalysisToDevis } from "@/services/agent"

export async function POST(request: NextRequest) {
  try {
    const { requestId, userId } = await request.json()

    if (!requestId || isNaN(requestId)) {
      return NextResponse.json({ error: "ID de demande invalide" }, { status: 400 })
    }

    if (!userId || isNaN(userId)) {
      return NextResponse.json({ error: "ID utilisateur invalide" }, { status: 400 })
    }

    const result = await convertAnalysisToDevis(requestId, userId)

    return NextResponse.json(result)
  } catch (error: any) {
    console.error("Erreur lors de la conversion en devis:", error)
    return NextResponse.json({ error: error.message || "Erreur lors de la conversion en devis" }, { status: 500 })
  }
}
