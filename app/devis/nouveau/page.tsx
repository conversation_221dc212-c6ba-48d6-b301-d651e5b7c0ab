"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar } from "@/components/ui/calendar"
import { fr } from "date-fns/locale"
import { format } from "date-fns"
import { CalendarIcon, ChevronDown, ChevronUp, Plus, Trash2, Download } from "lucide-react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { useState } from "react"

export default function NouveauDevisPage() {
  const [date, setDate] = useState<Date>(new Date())
  const [validite, setValidite] = useState<Date>(new Date(Date.now() + 30 * 24 * 60 * 60 * 1000))
  const [sections, setSections] = useState([
    {
      id: 1,
      titre: "Préparation du chantier",
      description: "Travaux préparatoires",
      lignes: [
        {
          id: 1,
          designation: "Installation du chantier",
          description: "Mise en place des équipements et sécurisation",
          quantite: 1,
          unite: "forfait",
          prixUnitaire: 350,
          tva: 20,
        },
      ],
    },
  ])

  const ajouterSection = () => {
    setSections([
      ...sections,
      {
        id: Date.now(),
        titre: "Nouvelle section",
        description: "",
        lignes: [],
      },
    ])
  }

  const ajouterLigne = (sectionId: number) => {
    setSections(
      sections.map((section) => {
        if (section.id === sectionId) {
          return {
            ...section,
            lignes: [
              ...section.lignes,
              {
                id: Date.now(),
                designation: "Nouvelle ligne",
                description: "",
                quantite: 1,
                unite: "unité",
                prixUnitaire: 0,
                tva: 20,
              },
            ],
          }
        }
        return section
      }),
    )
  }

  const supprimerSection = (sectionId: number) => {
    setSections(sections.filter((section) => section.id !== sectionId))
  }

  const supprimerLigne = (sectionId: number, ligneId: number) => {
    setSections(
      sections.map((section) => {
        if (section.id === sectionId) {
          return {
            ...section,
            lignes: section.lignes.filter((ligne) => ligne.id !== ligneId),
          }
        }
        return section
      }),
    )
  }

  const calculerTotalHT = () => {
    return sections.reduce((total, section) => {
      return (
        total +
        section.lignes.reduce((sectionTotal, ligne) => {
          return sectionTotal + ligne.quantite * ligne.prixUnitaire
        }, 0)
      )
    }, 0)
  }

  const calculerTotalTTC = () => {
    return sections.reduce((total, section) => {
      return (
        total +
        section.lignes.reduce((sectionTotal, ligne) => {
          return sectionTotal + ligne.quantite * ligne.prixUnitaire * (1 + ligne.tva / 100)
        }, 0)
      )
    }, 0)
  }

  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Nouveau devis</h1>
        <div className="flex gap-2">
          <Button variant="outline">Enregistrer brouillon</Button>
          <Button>Finaliser le devis</Button>
        </div>
      </div>

      <Tabs defaultValue="informations" className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="informations">Informations</TabsTrigger>
          <TabsTrigger value="contenu">Contenu du devis</TabsTrigger>
          <TabsTrigger value="apercu">Aperçu</TabsTrigger>
        </TabsList>
        <TabsContent value="informations">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Informations générales</CardTitle>
                <CardDescription>Informations de base du devis</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="reference">Référence</Label>
                  <Input id="reference" placeholder="DEV-2023-043" />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="date">Date d'émission</Label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button variant="outline" className="w-full justify-start text-left font-normal">
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {date ? format(date, "PPP", { locale: fr }) : "Sélectionner une date"}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0">
                        <Calendar
                          mode="single"
                          selected={date}
                          onSelect={(date) => date && setDate(date)}
                          locale={fr}
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="validite">Date de validité</Label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button variant="outline" className="w-full justify-start text-left font-normal">
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {validite ? format(validite, "PPP", { locale: fr }) : "Sélectionner une date"}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0">
                        <Calendar
                          mode="single"
                          selected={validite}
                          onSelect={(date) => date && setValidite(date)}
                          locale={fr}
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="introduction">Introduction</Label>
                  <Textarea id="introduction" placeholder="Suite à votre demande, nous vous proposons..." rows={3} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="conclusion">Conclusion</Label>
                  <Textarea
                    id="conclusion"
                    placeholder="En espérant que cette proposition réponde à vos attentes..."
                    rows={3}
                  />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Client et projet</CardTitle>
                <CardDescription>Informations sur le client et le projet</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="client">Client</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Sélectionner un client" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="client1">Martin Dupont</SelectItem>
                      <SelectItem value="client2">Entreprise ABC</SelectItem>
                      <SelectItem value="client3">Sophie Lefebvre</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="projet">Projet</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Sélectionner un projet" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="projet1">Rénovation salle de bain</SelectItem>
                      <SelectItem value="projet2">Construction mur</SelectItem>
                      <SelectItem value="projet3">Installation électrique</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="adresse">Adresse du chantier</Label>
                  <Textarea id="adresse" placeholder="123 rue des Artisans, 75001 Paris" rows={3} />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        <TabsContent value="contenu">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Contenu du devis</CardTitle>
                <CardDescription>Ajoutez des sections et des lignes à votre devis</CardDescription>
              </div>
              <Button onClick={ajouterSection}>
                <Plus className="mr-2 h-4 w-4" />
                Ajouter une section
              </Button>
            </CardHeader>
            <CardContent className="space-y-6">
              {sections.map((section, index) => (
                <div key={section.id} className="rounded-md border p-4">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-2">
                      <div className="flex flex-col items-center">
                        <Button variant="ghost" size="icon" className="h-6 w-6">
                          <ChevronUp className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon" className="h-6 w-6">
                          <ChevronDown className="h-4 w-4" />
                        </Button>
                      </div>
                      <div className="font-medium">Section {index + 1}</div>
                    </div>
                    <Button variant="ghost" size="icon" onClick={() => supprimerSection(section.id)}>
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="grid gap-4 mb-4">
                    <div className="space-y-2">
                      <Label htmlFor={`section-${section.id}-titre`}>Titre</Label>
                      <Input
                        id={`section-${section.id}-titre`}
                        value={section.titre}
                        onChange={(e) => {
                          setSections(
                            sections.map((s) => {
                              if (s.id === section.id) {
                                return { ...s, titre: e.target.value }
                              }
                              return s
                            }),
                          )
                        }}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor={`section-${section.id}-description`}>Description</Label>
                      <Textarea
                        id={`section-${section.id}-description`}
                        value={section.description}
                        onChange={(e) => {
                          setSections(
                            sections.map((s) => {
                              if (s.id === section.id) {
                                return { ...s, description: e.target.value }
                              }
                              return s
                            }),
                          )
                        }}
                      />
                    </div>
                  </div>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-[300px]">Désignation</TableHead>
                        <TableHead>Quantité</TableHead>
                        <TableHead>Unité</TableHead>
                        <TableHead>Prix unitaire HT</TableHead>
                        <TableHead>TVA</TableHead>
                        <TableHead>Total HT</TableHead>
                        <TableHead></TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {section.lignes.map((ligne) => (
                        <TableRow key={ligne.id}>
                          <TableCell>
                            <Input
                              value={ligne.designation}
                              onChange={(e) => {
                                setSections(
                                  sections.map((s) => {
                                    if (s.id === section.id) {
                                      return {
                                        ...s,
                                        lignes: s.lignes.map((l) => {
                                          if (l.id === ligne.id) {
                                            return { ...l, designation: e.target.value }
                                          }
                                          return l
                                        }),
                                      }
                                    }
                                    return s
                                  }),
                                )
                              }}
                            />
                          </TableCell>
                          <TableCell>
                            <Input
                              type="number"
                              value={ligne.quantite}
                              onChange={(e) => {
                                setSections(
                                  sections.map((s) => {
                                    if (s.id === section.id) {
                                      return {
                                        ...s,
                                        lignes: s.lignes.map((l) => {
                                          if (l.id === ligne.id) {
                                            return { ...l, quantite: Number.parseFloat(e.target.value) || 0 }
                                          }
                                          return l
                                        }),
                                      }
                                    }
                                    return s
                                  }),
                                )
                              }}
                            />
                          </TableCell>
                          <TableCell>
                            <Select
                              value={ligne.unite}
                              onValueChange={(value) => {
                                setSections(
                                  sections.map((s) => {
                                    if (s.id === section.id) {
                                      return {
                                        ...s,
                                        lignes: s.lignes.map((l) => {
                                          if (l.id === ligne.id) {
                                            return { ...l, unite: value }
                                          }
                                          return l
                                        }),
                                      }
                                    }
                                    return s
                                  }),
                                )
                              }}
                            >
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="unité">Unité</SelectItem>
                                <SelectItem value="m²">m²</SelectItem>
                                <SelectItem value="m">m</SelectItem>
                                <SelectItem value="forfait">Forfait</SelectItem>
                                <SelectItem value="heure">Heure</SelectItem>
                              </SelectContent>
                            </Select>
                          </TableCell>
                          <TableCell>
                            <Input
                              type="number"
                              value={ligne.prixUnitaire}
                              onChange={(e) => {
                                setSections(
                                  sections.map((s) => {
                                    if (s.id === section.id) {
                                      return {
                                        ...s,
                                        lignes: s.lignes.map((l) => {
                                          if (l.id === ligne.id) {
                                            return { ...l, prixUnitaire: Number.parseFloat(e.target.value) || 0 }
                                          }
                                          return l
                                        }),
                                      }
                                    }
                                    return s
                                  }),
                                )
                              }}
                            />
                          </TableCell>
                          <TableCell>
                            <Select
                              value={ligne.tva.toString()}
                              onValueChange={(value) => {
                                setSections(
                                  sections.map((s) => {
                                    if (s.id === section.id) {
                                      return {
                                        ...s,
                                        lignes: s.lignes.map((l) => {
                                          if (l.id === ligne.id) {
                                            return { ...l, tva: Number.parseFloat(value) }
                                          }
                                          return l
                                        }),
                                      }
                                    }
                                    return s
                                  }),
                                )
                              }}
                            >
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="20">20%</SelectItem>
                                <SelectItem value="10">10%</SelectItem>
                                <SelectItem value="5.5">5.5%</SelectItem>
                                <SelectItem value="0">0%</SelectItem>
                              </SelectContent>
                            </Select>
                          </TableCell>
                          <TableCell className="font-medium">
                            {(ligne.quantite * ligne.prixUnitaire).toLocaleString("fr-FR")} €
                          </TableCell>
                          <TableCell>
                            <Button variant="ghost" size="icon" onClick={() => supprimerLigne(section.id, ligne.id)}>
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                  <div className="mt-4">
                    <Button variant="outline" onClick={() => ajouterLigne(section.id)}>
                      <Plus className="mr-2 h-4 w-4" />
                      Ajouter une ligne
                    </Button>
                  </div>
                </div>
              ))}
            </CardContent>
            <CardFooter className="flex justify-between border-t p-4">
              <div>
                <p className="text-sm text-muted-foreground">Total HT</p>
                <p className="text-lg font-bold">{calculerTotalHT().toLocaleString("fr-FR")} €</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Total TTC</p>
                <p className="text-lg font-bold">{calculerTotalTTC().toLocaleString("fr-FR")} €</p>
              </div>
            </CardFooter>
          </Card>
        </TabsContent>
        <TabsContent value="apercu">
          <Card>
            <CardHeader>
              <CardTitle>Aperçu du devis</CardTitle>
              <CardDescription>Visualisez votre devis avant de l'envoyer</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="border rounded-md p-8">
                <div className="flex justify-between mb-8">
                  <div>
                    <h2 className="text-xl font-bold">BTP Manager</h2>
                    <p>123 rue des Artisans</p>
                    <p>75001 Paris</p>
                    <p><EMAIL></p>
                  </div>
                  <div className="text-right">
                    <h3 className="text-lg font-bold">DEVIS</h3>
                    <p>Référence: DEV-2023-043</p>
                    <p>Date: {format(date, "dd/MM/yyyy")}</p>
                    <p>Validité: {format(validite, "dd/MM/yyyy")}</p>
                  </div>
                </div>
                <div className="mb-8">
                  <h3 className="font-bold mb-2">Client</h3>
                  <p>Martin Dupont</p>
                  <p>456 avenue des Clients</p>
                  <p>75002 Paris</p>
                </div>
                <div className="mb-8">
                  <h3 className="font-bold mb-2">Projet</h3>
                  <p>Rénovation salle de bain</p>
                  <p>456 avenue des Clients, 75002 Paris</p>
                </div>
                <div className="mb-4">
                  <p>Suite à votre demande, nous vous proposons...</p>
                </div>
                {sections.map((section, index) => (
                  <div key={section.id} className="mb-6">
                    <h3 className="font-bold border-b pb-2 mb-2">{section.titre}</h3>
                    {section.description && <p className="mb-4">{section.description}</p>}
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead className="w-[300px]">Désignation</TableHead>
                          <TableHead>Quantité</TableHead>
                          <TableHead>Unité</TableHead>
                          <TableHead>Prix unitaire HT</TableHead>
                          <TableHead>TVA</TableHead>
                          <TableHead>Total HT</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {section.lignes.map((ligne) => (
                          <TableRow key={ligne.id}>
                            <TableCell>{ligne.designation}</TableCell>
                            <TableCell>{ligne.quantite}</TableCell>
                            <TableCell>{ligne.unite}</TableCell>
                            <TableCell>{ligne.prixUnitaire.toLocaleString("fr-FR")} €</TableCell>
                            <TableCell>{ligne.tva}%</TableCell>
                            <TableCell>{(ligne.quantite * ligne.prixUnitaire).toLocaleString("fr-FR")} €</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                ))}
                <div className="flex justify-end mb-8">
                  <div className="w-64">
                    <div className="flex justify-between py-2">
                      <span>Total HT</span>
                      <span className="font-medium">{calculerTotalHT().toLocaleString("fr-FR")} €</span>
                    </div>
                    <div className="flex justify-between py-2 border-t">
                      <span>TVA</span>
                      <span className="font-medium">
                        {(calculerTotalTTC() - calculerTotalHT()).toLocaleString("fr-FR")} €
                      </span>
                    </div>
                    <div className="flex justify-between py-2 border-t border-t-2">
                      <span className="font-bold">Total TTC</span>
                      <span className="font-bold">{calculerTotalTTC().toLocaleString("fr-FR")} €</span>
                    </div>
                  </div>
                </div>
                <div className="mb-8">
                  <p>En espérant que cette proposition réponde à vos attentes...</p>
                </div>
                <div className="grid grid-cols-2 gap-8 mb-8">
                  <div>
                    <h3 className="font-bold mb-2">Conditions de règlement</h3>
                    <p>Acompte de 30% à la commande</p>
                    <p>Solde à la fin des travaux</p>
                  </div>
                  <div>
                    <h3 className="font-bold mb-2">Signature client</h3>
                    <p className="mb-2">Bon pour accord</p>
                    <div className="border-dashed border-2 h-24 rounded-md"></div>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline">
                <Download className="mr-2 h-4 w-4" />
                Télécharger PDF
              </Button>
              <div className="flex gap-2">
                <Button variant="outline">Enregistrer brouillon</Button>
                <Button>Finaliser et envoyer</Button>
              </div>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
