"use client"

import { useEffect, useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Download, FileText } from "lucide-react"
import { type Devis, getDevis } from "@/services/devis"
import { useToast } from "@/hooks/use-toast"

export function DevisList() {
  const [devis, setDevis] = useState<Devis[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const { toast } = useToast()

  useEffect(() => {
    async function loadDevis() {
      try {
        const data = await getDevis()
        setDevis(data)
      } catch (error: any) {
        toast({
          title: "Erreur",
          description: error.message || "Impossible de charger les devis",
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    }

    loadDevis()
  }, [toast])

  if (isLoading) {
    return <div>Chargement des devis...</div>
  }

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Référence</TableHead>
          <TableHead>Date</TableHead>
          <TableHead>Validité</TableHead>
          <TableHead>Montant HT</TableHead>
          <TableHead>Montant TTC</TableHead>
          <TableHead>Statut</TableHead>
          <TableHead className="text-right">Actions</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {devis.length === 0 ? (
          <TableRow>
            <TableCell colSpan={7} className="text-center">
              Aucun devis trouvé
            </TableCell>
          </TableRow>
        ) : (
          devis.map((d) => (
            <TableRow key={d.id}>
              <TableCell className="font-medium">{d.reference}</TableCell>
              <TableCell>{new Date(d.date_creation).toLocaleDateString("fr-FR")}</TableCell>
              <TableCell>{new Date(d.date_validite).toLocaleDateString("fr-FR")}</TableCell>
              <TableCell>{d.montant_ht.toLocaleString("fr-FR")} €</TableCell>
              <TableCell>{d.montant_ttc.toLocaleString("fr-FR")} €</TableCell>
              <TableCell>
                <Badge
                  variant={
                    d.statut === "signé"
                      ? "success"
                      : d.statut === "envoyé"
                        ? "default"
                        : d.statut === "refusé"
                          ? "destructive"
                          : "outline"
                  }
                >
                  {d.statut}
                </Badge>
              </TableCell>
              <TableCell className="text-right">
                <Button variant="ghost" size="icon">
                  <FileText className="h-4 w-4" />
                  <span className="sr-only">Voir le devis</span>
                </Button>
                <Button variant="ghost" size="icon">
                  <Download className="h-4 w-4" />
                  <span className="sr-only">Télécharger</span>
                </Button>
              </TableCell>
            </TableRow>
          ))
        )}
      </TableBody>
    </Table>
  )
}
