"use client"

import type React from "react"

import { useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import Link from "next/link"
import { useSupabase } from "@/hooks/use-supabase"
import { useToast } from "@/hooks/use-toast"

interface AuthFormProps {
  type: "login" | "register"
}

export function AuthForm({ type }: AuthFormProps) {
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [nom, setNom] = useState("")
  const [prenom, setPrenom] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()
  const supabase = useSupabase()
  const { toast } = useToast()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      if (type === "login") {
        const { data, error } = await supabase.auth.signInWithPassword({
          email,
          password,
        })

        if (error) {
          throw error
        }

        toast({
          title: "Connexion réussie",
          description: "Vous êtes maintenant connecté",
        })

        // Force redirect to dashboard after successful login
        router.push("/dashboard")
      } else {
        // Inscription
        const { data, error } = await supabase.auth.signUp({
          email,
          password,
          options: {
            emailRedirectTo: `${window.location.origin}/auth/callback`,
            data: {
              first_name: prenom,
              last_name: nom,
            },
          },
        })

        if (error) {
          throw error
        }

        // Créer un enregistrement dans la table utilisateurs
        if (data.user) {
          // Récupérer l'ID de l'entreprise par défaut (la première entreprise)
          const { data: entreprises, error: entrepriseError } = await supabase
            .from("entreprises")
            .select("id")
            .order("id", { ascending: true })
            .limit(1)

          if (entrepriseError) {
            console.error("Erreur lors de la récupération de l'entreprise:", entrepriseError)
          } else if (entreprises && entreprises.length > 0) {
            const entrepriseId = entreprises[0].id

            // Créer l'utilisateur dans la table personnalisée
            const { error: userError } = await supabase.from("utilisateurs").insert([
              {
                entreprise_id: entrepriseId,
                nom: nom,
                prenom: prenom,
                email: email,
                role: "collaborateur",
                actif: true,
              },
            ])

            if (userError) {
              console.error("Erreur lors de la création de l'utilisateur dans la table personnalisée:", userError)
            }
          }
        }

        toast({
          title: "Inscription réussie",
          description: "Veuillez vérifier votre email pour confirmer votre compte",
        })

        // Redirect to login page after successful registration
        router.push("/login")
      }
    } catch (error: any) {
      toast({
        title: "Erreur",
        description: error.message || "Une erreur est survenue",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Card className="w-full max-w-md">
      <CardHeader className="space-y-1">
        <CardTitle className="text-2xl font-bold">BTP Manager</CardTitle>
        <CardDescription>
          {type === "login"
            ? "Connectez-vous à votre compte pour accéder à votre espace"
            : "Créez un compte pour commencer à utiliser BTP Manager"}
        </CardDescription>
      </CardHeader>
      <form onSubmit={handleSubmit}>
        <CardContent className="space-y-4">
          {type === "register" && (
            <>
              <div className="space-y-2">
                <Label htmlFor="nom">Nom</Label>
                <Input
                  id="nom"
                  value={nom}
                  onChange={(e) => setNom(e.target.value)}
                  placeholder="Dupont"
                  required={type === "register"}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="prenom">Prénom</Label>
                <Input
                  id="prenom"
                  value={prenom}
                  onChange={(e) => setPrenom(e.target.value)}
                  placeholder="Jean"
                  required={type === "register"}
                />
              </div>
            </>
          )}
          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              placeholder="<EMAIL>"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
            />
          </div>
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label htmlFor="password">Mot de passe</Label>
              {type === "login" && (
                <Link href="/reset-password" className="text-sm text-primary hover:underline">
                  Mot de passe oublié?
                </Link>
              )}
            </div>
            <Input
              id="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
            />
          </div>
        </CardContent>
        <CardFooter className="flex flex-col">
          <Button className="w-full" type="submit" disabled={isLoading}>
            {isLoading
              ? type === "login"
                ? "Connexion en cours..."
                : "Inscription en cours..."
              : type === "login"
                ? "Se connecter"
                : "S'inscrire"}
          </Button>
          <p className="mt-4 text-center text-sm text-muted-foreground">
            {type === "login" ? "Pas encore de compte? " : "Vous avez déjà un compte? "}
            <Link href={type === "login" ? "/register" : "/login"} className="text-primary hover:underline">
              {type === "login" ? "Créer un compte" : "Se connecter"}
            </Link>
          </p>
        </CardFooter>
      </form>
    </Card>
  )
}
