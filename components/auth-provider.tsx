"use client"

import type React from "react"

import { createContext, useContext, useEffect, useState } from "react"
import { useRouter, usePathname } from "next/navigation"
import { supabase } from "@/lib/supabase"
import type { Session, User } from "@supabase/supabase-js"

type AuthContextType = {
  user: User | null
  session: Session | null
  isLoading: boolean
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  session: null,
  isLoading: true,
})

export const useAuth = () => useContext(AuthContext)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const router = useRouter()
  const pathname = usePathname()

  useEffect(() => {
    // Get session on initial load
    const getInitialSession = async () => {
      try {
        const { data, error } = await supabase.auth.getSession()

        if (error) {
          console.error("Error getting session:", error)
        }

        setSession(data.session)
        setUser(data.session?.user ?? null)

        // If user is logged in and on login/register page, redirect to dashboard
        if (data.session && (pathname === "/login" || pathname === "/register")) {
          router.push("/dashboard")
        }
      } catch (error) {
        console.error("Unexpected error during getSession:", error)
      } finally {
        setIsLoading(false)
      }
    }

    getInitialSession()

    // Set up auth state listener
    const { data: authListener } = supabase.auth.onAuthStateChange(async (event, newSession) => {
      console.log("Auth state changed:", event)
      setSession(newSession)
      setUser(newSession?.user ?? null)

      // Handle auth state changes
      if (event === "SIGNED_IN" && (pathname === "/login" || pathname === "/register")) {
        router.push("/dashboard")
      } else if (event === "SIGNED_OUT") {
        router.push("/login")
      }

      // Force a router refresh to update server components
      router.refresh()
    })

    // Clean up subscription
    return () => {
      authListener.subscription.unsubscribe()
    }
  }, [router, pathname])

  return <AuthContext.Provider value={{ user, session, isLoading }}>{children}</AuthContext.Provider>
}
