"use client"

import type React from "react"

import { createContext, useContext, useEffect, useState } from "react"
import { useRouter, usePathname } from "next/navigation"
import { supabase } from "@/lib/supabase"
import type { Session, User } from "@supabase/supabase-js"

type AuthContextType = {
  user: User | null
  session: Session | null
  isLoading: boolean
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  session: null,
  isLoading: true,
})

export const useAuth = () => useContext(AuthContext)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const router = useRouter()
  const pathname = usePathname()

  useEffect(() => {
    // Get session on initial load
    const getInitialSession = async () => {
      try {
        console.log("Getting initial session, current pathname:", pathname)
        const { data, error } = await supabase.auth.getSession()

        if (error) {
          console.error("Error getting session:", error)
        }

        console.log("Initial session data:", data.session ? "exists" : "null")
        setSession(data.session)
        setUser(data.session?.user ?? null)

        // If user is logged in and on login/register page, redirect to dashboard
        if (data.session && (pathname === "/login" || pathname === "/register")) {
          console.log("Initial session exists and on auth page, redirecting to dashboard")
          router.push("/dashboard")
        }
      } catch (error) {
        console.error("Unexpected error during getSession:", error)
      } finally {
        setIsLoading(false)
      }
    }

    getInitialSession()

    // Set up auth state listener
    const { data: authListener } = supabase.auth.onAuthStateChange(async (event, newSession) => {
      console.log("Auth state changed:", event, "Current pathname:", pathname)
      console.log("New session:", newSession ? "exists" : "null")

      setSession(newSession)
      setUser(newSession?.user ?? null)

      // Handle auth state changes
      if (event === "SIGNED_IN") {
        console.log("User signed in, current path:", pathname)
        if (pathname === "/login" || pathname === "/register") {
          console.log("Redirecting to dashboard from auth page")
          router.push("/dashboard")
        }
      } else if (event === "SIGNED_OUT") {
        console.log("User signed out, redirecting to login")
        router.push("/login")
      }

      // Force a router refresh to update server components
      router.refresh()
    })

    // Clean up subscription
    return () => {
      authListener.subscription.unsubscribe()
    }
  }, [router, pathname])

  return <AuthContext.Provider value={{ user, session, isLoading }}>{children}</AuthContext.Provider>
}
