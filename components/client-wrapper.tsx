"use client"

import type React from "react"

import { usePathname } from "next/navigation"
import Sidebar from "@/components/sidebar"
import Header from "@/components/header"
import { DemoModeBanner } from "@/components/demo-mode-banner"
import { SupabaseProvider } from "@/components/supabase-provider"
import { isDemoMode } from "@/lib/demo-mode"

export default function ClientWrapper({
  children,
}: {
  children: React.ReactNode
}) {
  const pathname = usePathname()
  const isAuthPage = pathname === "/login" || pathname === "/register" || pathname.startsWith("/auth")
  const isAdminPage = pathname.startsWith("/admin")

  // Ensure children is not a function
  const renderChildren = typeof children === "function" ? children() : children

  // Pages d'authentification
  if (isAuthPage) {
    return <SupabaseProvider>{renderChildren}</SupabaseProvider>
  }

  // Pages d'administration
  if (isAdminPage) {
    return (
      <SupabaseProvider>
        <div className="min-h-screen p-6">
          {isDemoMode() && <DemoModeBanner />}
          {renderChildren}
        </div>
      </SupabaseProvider>
    )
  }

  // Pages normales avec sidebar et header
  return (
    <SupabaseProvider>
      <div className="flex min-h-screen flex-col">
        <Header />
        <div className="flex flex-1">
          <Sidebar />
          <main className="flex-1 p-6">
            {isDemoMode() && <DemoModeBanner />}
            {renderChildren}
          </main>
        </div>
      </div>
    </SupabaseProvider>
  )
}
