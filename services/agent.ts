import { supabase } from "@/lib/supabase"
import { isDemoMode } from "@/lib/demo-mode"
import { generateText } from "ai"
import { aiConfig, handleAIError } from "@/lib/ai-config"

export interface Agent {
  id: number
  request_id: number
  agent_response: string
  created_at: string
  updated_at: string
}

// Get the bucket name from environment variable
const BUCKET_NAME = process.env.NEXT_PUBLIC_SUPABASE_CLIENT_FILES_BUCKET || "client-files"

// Enhanced system prompt for AI analysis with Gemini 2.5 Pro capabilities
const ANALYSIS_SYSTEM_PROMPT = `## System Prompt: Assistant de Gestion de Projets BTP - Gemini 2.5 Pro

Tu es un assistant IA de nouvelle génération spécialisé dans la gestion de projets du secteur BTP (Bâtiment et Travaux Publics). Utilise tes capacités avancées de raisonnement et d'analyse pour fournir des insights détaillés et des recommandations pratiques.

### Capacités Avancées:
- Analyse multimodale des documents (texte, images, plans)
- Raisonnement complexe sur les contraintes techniques et réglementaires
- Optimisation des ressources et planification intelligente
- Détection proactive des risques et opportunités

### 1. Points d'Attention Critiques
- Identifie et analyse les risques potentiels avec une évaluation de probabilité et d'impact
- Évalue les points de vigilance réglementaires (RT 2012, RE 2020, accessibilité PMR, etc.)
- Signale les éléments techniques nécessitant une attention particulière
- Propose des solutions de mitigation hiérarchisées par efficacité

### 2. Suggestions d'Implantation Optimisées
- Recommande l'organisation optimale basée sur les flux de travail
- Propose des configurations efficientes avec calculs d'optimisation
- Analyse les contraintes géotechniques et environnementales
- Optimise les accès, stockage et zones de travail

### 3. Planification Matérielle Intelligente
- Établis des listes détaillées avec quantifications précises
- Planifie les approvisionnements selon le planning critique
- Classe les matériaux par criticité et délais d'approvisionnement
- Indique les spécifications techniques et normes applicables

### 4. Recherche de Fournisseurs et Optimisation Commerciale
- Identifie les types de fournisseurs avec critères de sélection
- Suggère des stratégies d'achat groupé et négociation
- Analyse les tendances de marché et saisonnalité
- Propose des alternatives et substitutions possibles

### 5. Gestion RH et Coordination Avancée
- Évalue les besoins en compétences avec matrice de compétences
- Propose des structures d'équipe optimisées
- Identifie les goulots d'étranglement en ressources humaines
- Recommande des approches de formation et montée en compétences

### Format de Réponse:
Utilise un format structuré avec:
- Titres et sous-titres clairs
- Listes à puces hiérarchisées
- Données chiffrées et métriques quand pertinent
- Codes couleur conceptuels (🔴 Critique, 🟡 Attention, 🟢 Optimal)
- Vocabulaire technique précis du BTP

Si des informations sont manquantes, indique-le clairement et suggère les données complémentaires nécessaires.`

// Enhanced system prompt for converting analysis to devis with structured output
const DEVIS_CONVERSION_PROMPT = `## System Prompt: Assistant de Conversion en Devis BTP - Gemini 2.5 Pro

Tu es un assistant IA spécialisé dans la transformation d'analyses de projets BTP en structures de devis détaillées et professionnelles.

### Instructions Avancées:
- Extrais TOUS les éléments quantifiables de l'analyse
- Structure les informations selon les phases de construction
- Applique les bonnes pratiques de chiffrage BTP
- Respecte la hiérarchie des lots et sous-lots

### Sections Standards BTP:
1. **Préparation et Installation de Chantier**
2. **Gros Œuvre** (fondations, structure, maçonnerie)
3. **Charpente et Couverture**
4. **Étanchéité et Isolation**
5. **Cloisons et Doublages**
6. **Menuiseries Extérieures**
7. **Plomberie et Chauffage**
8. **Électricité**
9. **Revêtements de Sol**
10. **Peinture et Finitions**
11. **Menuiseries Intérieures**
12. **Équipements et Accessoires**
13. **Espaces Verts et VRD**
14. **Main d'Œuvre Spécialisée**

### Unités Standards:
- Surface: m², m² utile, m² SHON
- Volume: m³, m³ de béton, m³ de terrassement
- Linéaire: ml, ml de canalisation, ml de clôture
- Quantité: u (unité), lot, forfait
- Temps: h (heures), j (jours), semaine

Fournis EXCLUSIVEMENT du JSON valide sans explication. Structure chaque ligne avec:
- designation (claire et professionnelle)
- description (détaillée avec spécifications techniques)
- quantite (numérique, basée sur l'analyse)
- unite (selon standards BTP)
- prix_unitaire_ht (null - à remplir par l'utilisateur)
- taux_tva (20 par défaut, 10 pour certains travaux)

Format JSON attendu:
{
  "sections": [
    {
      "titre_section": "Nom de la section",
      "description": "Description de la section",
      "lignes_devis": [
        {
          "designation": "Désignation précise",
          "description": "Description technique détaillée",
          "quantite": 0,
          "unite": "unité",
          "prix_unitaire_ht": null,
          "taux_tva": 20
        }
      ]
    }
  ]
}`

// Get agent analysis for a request
export async function getAgentAnalysis(requestId: number): Promise<Agent | null> {
  if (isDemoMode()) {
    return {
      id: 1,
      request_id: requestId,
      agent_response: "Analyse de démonstration pour la demande #" + requestId,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    }
  }

  try {
    const { data, error } = await supabase.from("agent").select("*").eq("request_id", requestId).single()

    if (error && error.code !== "PGRST116") {
      console.error("Erreur lors de la récupération de l'analyse:", error)
      throw error
    }

    return data as Agent | null
  } catch (error) {
    console.error("Erreur lors de la récupération de l'analyse:", error)
    throw error
  }
}

// Convert file to base64 for AI processing
async function fileToBase64(filePath: string): Promise<string> {
  try {
    const { data, error } = await supabase.storage.from(BUCKET_NAME).download(filePath)

    if (error) {
      console.error("Erreur lors du téléchargement du fichier:", error)
      throw error
    }

    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => {
        const base64 = reader.result as string
        // Remove the data URL prefix to get just the base64 string
        const base64Data = base64.split(",")[1]
        resolve(base64Data)
      }
      reader.onerror = reject
      reader.readAsDataURL(data)
    })
  } catch (error) {
    console.error("Erreur lors de la conversion en base64:", error)
    throw error
  }
}

// Analyze request with AI using Gemini 2.5 Pro
export async function analyzeRequestWithAI(requestId: number): Promise<Agent> {
  if (isDemoMode()) {
    console.log("Mode démo: simulation d'analyse IA")
    return {
      id: 1,
      request_id: requestId,
      agent_response: `# Analyse IA Gemini 2.5 Pro - Demande #${requestId}

## 🔍 Analyse Avancée du Projet

### Points d'Attention Critiques
🔴 **Risques Élevés:**
- Vérification de la conformité réglementaire RT 2020
- Analyse des risques de sécurité sur site
- Contrôle de la faisabilité technique structurelle

🟡 **Points de Vigilance:**
- Coordination des corps d'état
- Respect des délais d'approvisionnement
- Gestion des interfaces techniques

### Suggestions d'Implantation Optimisées
🟢 **Configuration Recommandée:**
- Zone de stockage matériaux: 150m² minimum
- Accès engins: largeur 4m, rayon de braquage 12m
- Base vie: 30m² pour équipe de 8 personnes
- Évacuation des déchets: benne 20m³

### Planification Matérielle Intelligente
**Gros Œuvre:**
- Béton C25/30: 50m³ ±10%
- Acier HA: 2,5 tonnes
- Coffrages: 200m² de surface

**Équipements:**
- Grue mobile 25T: 5 jours
- Bétonnière 350L: location mensuelle
- Équipements de sécurité: 8 personnes

### Optimisation Commerciale
**Fournisseurs Prioritaires:**
- Centrale à béton locale (rayon 30km)
- Négoce matériaux avec livraison
- Loueur d'engins certifié

**Stratégies d'Achat:**
- Commande groupée béton: -8% sur prix unitaire
- Négociation forfait location: -15% sur tarif journalier

### Gestion RH Avancée
**Équipe Optimale:**
- Chef de chantier: 1 (niveau II minimum)
- Maçons qualifiés: 3 (CAP + 5 ans exp.)
- Manœuvres: 2
- Coffreur-ferrailleur: 2

**Planning Prévisionnel:** 4 semaines ±1 semaine`,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    }
  }

  try {
    // Get request data with plans and photos
    const { data: request, error: requestError } = await supabase
      .from("requests")
      .select(`
        *,
        client:clients(id, nom, prenom, societe, type)
      `)
      .eq("id", requestId)
      .single()

    if (requestError) {
      console.error("Erreur lors de la récupération de la demande:", requestError)
      throw requestError
    }

    // Get plans
    const { data: plans, error: plansError } = await supabase.from("plans").select("*").eq("request_id", requestId)

    if (plansError) {
      console.error("Erreur lors de la récupération des plans:", plansError)
      throw plansError
    }

    // Get photos
    const { data: photos, error: photosError } = await supabase.from("photos").select("*").eq("request_id", requestId)

    if (photosError) {
      console.error("Erreur lors de la récupération des photos:", photosError)
      throw photosError
    }

    // Prepare enhanced content for AI
    let content = `## Analyse de Projet BTP - Demande #${requestId}

### Description du Projet:
${request.description}

### Informations Client:
`

    if (request.client) {
      content += `- **Type:** ${request.client.type}
- **Nom:** ${
        request.client.type === "particulier"
          ? `${request.client.prenom} ${request.client.nom}`
          : request.client.societe
      }
`
    }

    // Add information about plans and photos with enhanced context
    if (plans && plans.length > 0) {
      content += `
### Documents Plans Disponibles (${plans.length}):
`
      plans.forEach((plan, index) => {
        content += `${index + 1}. **${plan.file_name}** (${plan.file_type})
   - Description: ${plan.description || "Non spécifiée"}
   - Date d'upload: ${new Date(plan.uploaded_at).toLocaleDateString("fr-FR")}
`
      })
    }

    if (photos && photos.length > 0) {
      content += `
### Photos Disponibles (${photos.length}):
`
      photos.forEach((photo, index) => {
        content += `${index + 1}. **${photo.file_name}**
   - Description: ${photo.description || "Non spécifiée"}
   - Date d'upload: ${new Date(photo.uploaded_at).toLocaleDateString("fr-FR")}
`
      })
    }

    content += `

### Instructions d'Analyse:
Effectue une analyse complète et détaillée de ce projet BTP en utilisant tes capacités avancées de raisonnement. Fournis des recommandations concrètes et chiffrées basées sur les bonnes pratiques du secteur.`

    // Call Gemini 2.5 Pro with enhanced settings
    const { text } = await generateText({
      model: aiConfig.model,
      system: ANALYSIS_SYSTEM_PROMPT,
      prompt: content,
      ...aiConfig.analysisSettings,
    })

    // Store the analysis result
    const { data: existingAnalysis } = await supabase.from("agent").select("id").eq("request_id", requestId).single()

    let agentData: Agent

    if (existingAnalysis) {
      // Update existing analysis
      const { data, error } = await supabase
        .from("agent")
        .update({
          agent_response: text,
          updated_at: new Date().toISOString(),
        })
        .eq("request_id", requestId)
        .select()
        .single()

      if (error) {
        console.error("Erreur lors de la mise à jour de l'analyse:", error)
        throw error
      }

      agentData = data as Agent
    } else {
      // Create new analysis
      const { data, error } = await supabase
        .from("agent")
        .insert([
          {
            request_id: requestId,
            agent_response: text,
          },
        ])
        .select()
        .single()

      if (error) {
        console.error("Erreur lors de la création de l'analyse:", error)
        throw error
      }

      agentData = data as Agent
    }

    return agentData
  } catch (error: any) {
    console.error("Erreur lors de l'analyse IA:", error)

    // Use enhanced error handling
    const errorMessage = handleAIError(error)
    throw new Error(errorMessage)
  }
}

// Convert AI analysis to devis structure using Gemini 2.5 Pro
export async function convertAnalysisToDevis(requestId: number, userId: number): Promise<any> {
  if (isDemoMode()) {
    console.log("Mode démo: simulation de conversion en devis")
    return { id: 1, message: "Devis créé en mode démo avec Gemini 2.5 Pro" }
  }

  try {
    // Get the AI analysis
    const analysis = await getAgentAnalysis(requestId)
    if (!analysis) {
      throw new Error("Aucune analyse IA trouvée pour cette demande")
    }

    // Enhanced prompt for devis conversion
    const conversionPrompt = `## Conversion d'Analyse BTP en Structure de Devis

### Analyse Source:
${analysis.agent_response}

### Instructions:
Convertis cette analyse en structure de devis professionnelle. Extrais TOUS les éléments quantifiables et organise-les selon les lots BTP standards. Assure-toi que chaque ligne de devis soit précise et professionnelle.`

    // Call Gemini 2.5 Pro for structured conversion
    const { text } = await generateText({
      model: aiConfig.model,
      system: DEVIS_CONVERSION_PROMPT,
      prompt: conversionPrompt,
      ...aiConfig.structuredSettings,
    })

    // Parse the JSON response with enhanced error handling
    let devisStructure
    try {
      // Clean the response to ensure it's valid JSON
      const cleanedText = text.trim()
      const jsonStart = cleanedText.indexOf("{")
      const jsonEnd = cleanedText.lastIndexOf("}") + 1
      const jsonText = cleanedText.slice(jsonStart, jsonEnd)

      devisStructure = JSON.parse(jsonText)
    } catch (parseError) {
      console.error("Erreur lors du parsing JSON:", parseError)
      console.error("Réponse IA:", text)
      throw new Error("Erreur lors de l'analyse de la réponse IA. Format JSON invalide.")
    }

    // Validate the structure
    if (!devisStructure.sections || !Array.isArray(devisStructure.sections)) {
      throw new Error("Structure de devis invalide: sections manquantes")
    }

    // Check if a devis already exists for this request
    const { data: existingDevis } = await supabase.from("devis").select("id").eq("request_id", requestId).single()

    let devisId: number

    if (existingDevis) {
      // Update existing devis
      devisId = existingDevis.id

      // Clear existing sections and lines
      await supabase.from("sections_devis").delete().eq("devis_id", devisId)
    } else {
      // Create new devis
      const reference = `DEV-${Date.now()}`
      const { data: newDevis, error: devisError } = await supabase
        .from("devis")
        .insert([
          {
            request_id: requestId,
            utilisateur_id: userId,
            reference,
            statut: "brouillon",
            introduction: "Devis généré automatiquement par IA Gemini 2.5 Pro à partir de l'analyse technique",
            conclusion: "Merci de votre confiance. Ce devis a été optimisé par intelligence artificielle.",
            taux_remise: 0,
            montant_ht: 0,
            montant_ttc: 0,
            validite_jours: 30,
            conditions_acceptees: false,
            date_creation: new Date().toISOString(),
            date_validite: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
          },
        ])
        .select()
        .single()

      if (devisError) {
        console.error("Erreur lors de la création du devis:", devisError)
        throw devisError
      }

      devisId = newDevis.id
    }

    // Create sections and lines with enhanced validation
    for (const [sectionIndex, section] of devisStructure.sections.entries()) {
      if (!section.titre_section || !section.lignes_devis) {
        console.warn(`Section ${sectionIndex} invalide, ignorée`)
        continue
      }

      // Create section
      const { data: sectionData, error: sectionError } = await supabase
        .from("sections_devis")
        .insert([
          {
            devis_id: devisId,
            titre: section.titre_section,
            description: section.description || `Section générée automatiquement par IA Gemini 2.5 Pro`,
            ordre: sectionIndex + 1,
            montant_ht: 0,
          },
        ])
        .select()
        .single()

      if (sectionError) {
        console.error("Erreur lors de la création de la section:", sectionError)
        throw sectionError
      }

      // Create lines for this section with validation
      for (const [lineIndex, ligne] of section.lignes_devis.entries()) {
        if (!ligne.designation) {
          console.warn(`Ligne ${lineIndex} de la section ${section.titre_section} invalide, ignorée`)
          continue
        }

        const { error: lineError } = await supabase.from("lignes_devis").insert([
          {
            section_id: sectionData.id,
            designation: ligne.designation,
            reference: `REF-${Date.now()}-${sectionIndex}-${lineIndex}`,
            description: ligne.description || "",
            quantite: Number(ligne.quantite) || 1,
            unite: ligne.unite || "forfait",
            prix_unitaire_ht: Number(ligne.prix_unitaire_ht) || 0,
            taux_tva: Number(ligne.taux_tva) || 20,
            montant_ht: 0,
            montant_ttc: 0,
            ordre: lineIndex + 1,
          },
        ])

        if (lineError) {
          console.error("Erreur lors de la création de la ligne:", lineError)
          throw lineError
        }
      }
    }

    return {
      id: devisId,
      message: "Devis créé avec succès par IA Gemini 2.5 Pro",
      sectionsCount: devisStructure.sections.length,
    }
  } catch (error: any) {
    console.error("Erreur lors de la conversion en devis:", error)

    // Use enhanced error handling
    const errorMessage = handleAIError(error)
    throw new Error(errorMessage)
  }
}
