import { supabase } from "@/lib/supabase"
import { demoData, isDemoMode } from "@/lib/demo-mode"

export interface Devis {
  id: number
  projet_id: number
  utilisateur_id: number
  reference: string
  date_creation: string
  date_validite: string
  date_signature: string | null
  statut: "brouillon" | "envoyé" | "signé" | "refusé"
  introduction: string
  conclusion: string
  taux_remise: number
  montant_ht: number
  montant_ttc: number
  validite_jours: number
  conditions_acceptees: boolean
  signature_url: string | null
}

export interface SectionDevis {
  id: number
  devis_id: number
  titre: string
  description: string
  ordre: number
  montant_ht: number
}

export interface LigneDevis {
  id: number
  section_id: number
  catalogue_item_id: number | null
  designation: string
  reference: string
  description: string
  quantite: number
  unite: string
  prix_unitaire_ht: number
  taux_tva: number
  montant_ht: number
  montant_ttc: number
  ordre: number
}

export async function getDevis() {
  // Si le mode démo est activé, retourner les données de démonstration
  if (isDemoMode()) {
    console.log("Mode démo activé, utilisation des données de démonstration")
    return demoData.devis as Devi<PERSON>[]
  }

  try {
    const { data, error } = await supabase.from("devis").select("*").order("date_creation", { ascending: false })

    if (error) {
      console.error("Erreur Supabase:", error)
      throw error
    }

    return data as Devis[]
  } catch (error) {
    console.error("Erreur lors de la récupération des devis:", error)
    throw error
  }
}

export async function getDevisById(id: number) {
  // Si le mode démo est activé, retourner les données de démonstration
  if (isDemoMode()) {
    const devis = demoData.devis.find((d) => d.id === id)
    if (!devis) {
      throw new Error("Devis non trouvé")
    }
    return devis as Devis
  }

  try {
    const { data, error } = await supabase.from("devis").select("*").eq("id", id).single()

    if (error) {
      console.error("Erreur Supabase:", error)
      throw error
    }

    return data as Devis
  } catch (error) {
    console.error("Erreur lors de la récupération du devis:", error)
    throw error
  }
}

export async function createDevis(devis: Omit<Devis, "id" | "date_creation">) {
  // Si le mode démo est activé, simuler la création
  if (isDemoMode()) {
    console.log("Mode démo activé, simulation de création de devis")
    const devisList = [...demoData.devis]
    const newDevis: Devis = {
      id: devisList.length + 1,
      ...devis,
      date_creation: new Date().toISOString(),
    }
    return newDevis
  }

  try {
    const { data, error } = await supabase.from("devis").insert([devis]).select()

    if (error) {
      console.error("Erreur Supabase:", error)
      throw error
    }

    return data[0] as Devis
  } catch (error) {
    console.error("Erreur lors de la création du devis:", error)
    throw error
  }
}

export async function updateDevis(id: number, devis: Partial<Devis>) {
  // Si le mode démo est activé, simuler la mise à jour
  if (isDemoMode()) {
    console.log("Mode démo activé, simulation de mise à jour de devis")
    const devisList = [...demoData.devis]
    const index = devisList.findIndex((d) => d.id === id)
    if (index === -1) {
      throw new Error("Devis non trouvé")
    }
    const updatedDevis = { ...devisList[index], ...devis }
    return updatedDevis as Devis
  }

  try {
    const { data, error } = await supabase.from("devis").update(devis).eq("id", id).select()

    if (error) {
      console.error("Erreur Supabase:", error)
      throw error
    }

    return data[0] as Devis
  } catch (error) {
    console.error("Erreur lors de la mise à jour du devis:", error)
    throw error
  }
}

export async function deleteDevis(id: number) {
  // Si le mode démo est activé, simuler la suppression
  if (isDemoMode()) {
    console.log("Mode démo activé, simulation de suppression de devis")
    return true
  }

  try {
    const { error } = await supabase.from("devis").delete().eq("id", id)

    if (error) {
      console.error("Erreur Supabase:", error)
      throw error
    }

    return true
  } catch (error) {
    console.error("Erreur lors de la suppression du devis:", error)
    throw error
  }
}

// Fonctions pour les sections et lignes de devis
export async function getSectionsDevis(devisId: number) {
  if (isDemoMode()) {
    return [] as SectionDevis[]
  }

  try {
    const { data, error } = await supabase
      .from("sections_devis")
      .select("*")
      .eq("devis_id", devisId)
      .order("ordre", { ascending: true })

    if (error) {
      console.error("Erreur Supabase:", error)
      throw error
    }

    return data as SectionDevis[]
  } catch (error) {
    console.error("Erreur lors de la récupération des sections:", error)
    throw error
  }
}

export async function getLignesDevis(sectionId: number) {
  if (isDemoMode()) {
    return [] as LigneDevis[]
  }

  try {
    const { data, error } = await supabase
      .from("lignes_devis")
      .select("*")
      .eq("section_id", sectionId)
      .order("ordre", { ascending: true })

    if (error) {
      console.error("Erreur Supabase:", error)
      throw error
    }

    return data as LigneDevis[]
  } catch (error) {
    console.error("Erreur lors de la récupération des lignes:", error)
    throw error
  }
}

export async function createSectionDevis(section: Omit<SectionDevis, "id">) {
  if (isDemoMode()) {
    return {
      id: 1,
      ...section,
    } as SectionDevis
  }

  try {
    const { data, error } = await supabase.from("sections_devis").insert([section]).select()

    if (error) {
      console.error("Erreur Supabase:", error)
      throw error
    }

    return data[0] as SectionDevis
  } catch (error) {
    console.error("Erreur lors de la création de la section:", error)
    throw error
  }
}

export async function createLigneDevis(ligne: Omit<LigneDevis, "id">) {
  if (isDemoMode()) {
    return {
      id: 1,
      ...ligne,
    } as LigneDevis
  }

  try {
    const { data, error } = await supabase.from("lignes_devis").insert([ligne]).select()

    if (error) {
      console.error("Erreur Supabase:", error)
      throw error
    }

    return data[0] as LigneDevis
  } catch (error) {
    console.error("Erreur lors de la création de la ligne:", error)
    throw error
  }
}
