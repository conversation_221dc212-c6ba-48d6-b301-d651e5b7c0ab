import { createClient } from "@supabase/supabase-js"

// Singleton for the client Supabase
let supabaseInstance = null

// Function to initialize the client Supabase
export function initSupabaseClient() {
  // If an instance exists already, return it
  if (supabaseInstance) {
    return supabaseInstance
  }

  try {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
    const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

    // Vérifier si les variables d'environnement sont définies
    if (!supabaseUrl || !supabaseAnonKey) {
      console.warn("Variables d'environnement Supabase manquantes")
      return null
    }

    // Créer le client Supabase with persisted auth session
    supabaseInstance = createClient(supabaseUrl, supabaseAnonKey, {
      auth: {
        persistSession: true,
        storageKey: "btp-auth-token",
        autoRefreshToken: true,
        detectSessionInUrl: true,
      },
    })

    // Verify that the client was created successfully
    if (!supabaseInstance || !supabaseInstance.auth) {
      console.error("Erreur: Client Supabase ou objet auth non disponible après initialisation")
      return null
    }

    return supabaseInstance
  } catch (error) {
    console.error("Erreur lors de l'initialisation du client Supabase:", error)
    return null
  }
}

// Exporter l'instance du client Supabase
export const supabase = initSupabaseClient()

// Fonction pour vérifier la connexion à Supabase
export async function checkSupabaseConnection() {
  if (!supabase) {
    return {
      connected: false,
      error: "Client Supabase non initialisé",
    }
  }

  try {
    // Tenter une requête simple pour vérifier la connexion
    const { error } = await supabase.from("entreprises").select("id").limit(1)

    if (error) {
      return {
        connected: false,
        error: error.message,
      }
    }

    return {
      connected: true,
      error: null,
    }
  } catch (error) {
    return {
      connected: false,
      error: error?.message || "Erreur de connexion à Supabase",
    }
  }
}

export const connectionState = {
  offlineMode: {
    enabled: false,
    reason: "",
  },
}
